import zipfile
import os
from PIL import Image
from fpdf import FPD<PERSON>

def convert_zip_images_to_pdf(zip_path, output_pdf):
    # Extract images
    extract_path = "extracted_images"
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    # Collect image files
    image_files = [
        os.path.join(extract_path, f)
        for f in os.listdir(extract_path)
        if f.lower().endswith(('.png', '.jpg', '.jpeg'))
    ]
    image_files.sort()

    # Convert to PDF
    pdf = FPDF()
    for image in image_files:
        img = Image.open(image)
        width, height = img.size
        width, height = width * 0.264583, height * 0.264583  # pixels to mm
        pdf.add_page()
        pdf.image(image, 0, 0, width, height)

    pdf.output(output_pdf)
    print(f"PDF created: {output_pdf}")

# Example usage
convert_zip_images_to_pdf("Screenshot 2025-07-07 171612.zip", "output.pdf")
