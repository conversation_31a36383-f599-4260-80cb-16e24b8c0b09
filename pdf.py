import zipfile
import os
from PIL import Image
from fpdf import FPDF
import pytesseract
from pdf2image import convert_from_path
import fitz  # PyMuPDF

def convert_zip_images_to_pdf(zip_path, output_pdf):
    # Extract images
    extract_path = "extracted_images"
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    # Collect image files
    image_files = [
        os.path.join(extract_path, f)
        for f in os.listdir(extract_path)
        if f.lower().endswith(('.png', '.jpg', '.jpeg'))
    ]
    image_files.sort()

    # Convert to PDF
    pdf = FPDF()
    for image in image_files:
        img = Image.open(image)
        width, height = img.size
        width, height = width * 0.264583, height * 0.264583  # pixels to mm
        pdf.add_page()
        pdf.image(image, 0, 0, width, height)

    pdf.output(output_pdf)
    print(f"PDF created: {output_pdf}")

def extract_text_from_pdf_images(pdf_path, output_text_file=None):
    """
    Extract text from PDF images using OCR

    Args:
        pdf_path (str): Path to the PDF file
        output_text_file (str, optional): Path to save extracted text. If None, returns text as string

    Returns:
        str: Extracted text if output_text_file is None
    """
    try:
        # Method 1: Try using PyMuPDF for better performance
        doc = fitz.open(pdf_path)
        extracted_text = ""

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)

            # First try to extract text directly (for text-based PDFs)
            text = page.get_text()
            if text.strip():
                extracted_text += f"\n--- Page {page_num + 1} ---\n"
                extracted_text += text
            else:
                # If no text found, use OCR on the page image
                pix = page.get_pixmap()
                img_data = pix.tobytes("png")

                # Convert to PIL Image for OCR
                from io import BytesIO
                img = Image.open(BytesIO(img_data))

                # Perform OCR
                page_text = pytesseract.image_to_string(img, lang='eng')
                if page_text.strip():
                    extracted_text += f"\n--- Page {page_num + 1} (OCR) ---\n"
                    extracted_text += page_text

        doc.close()

    except Exception as e:
        print(f"Error with PyMuPDF method: {e}")
        print("Falling back to pdf2image method...")

        # Method 2: Fallback using pdf2image
        try:
            pages = convert_from_path(pdf_path)
            extracted_text = ""

            for i, page in enumerate(pages):
                # Perform OCR on each page
                page_text = pytesseract.image_to_string(page, lang='eng')
                if page_text.strip():
                    extracted_text += f"\n--- Page {i + 1} (OCR) ---\n"
                    extracted_text += page_text

        except Exception as e2:
            print(f"Error with pdf2image method: {e2}")
            return "Error: Could not extract text from PDF"

    # Save to file if specified
    if output_text_file:
        with open(output_text_file, 'w', encoding='utf-8') as f:
            f.write(extracted_text)
        print(f"Text extracted and saved to: {output_text_file}")
        return f"Text successfully extracted to {output_text_file}"

    return extracted_text


def extract_text_from_image(image_path, output_text_file=None):
    """
    Extract text from a single image using OCR

    Args:
        image_path (str): Path to the image file
        output_text_file (str, optional): Path to save extracted text. If None, returns text as string

    Returns:
        str: Extracted text if output_text_file is None
    """
    try:
        # Open image
        img = Image.open(image_path)

        # Perform OCR
        extracted_text = pytesseract.image_to_string(img, lang='eng')

        # Save to file if specified
        if output_text_file:
            with open(output_text_file, 'w', encoding='utf-8') as f:
                f.write(extracted_text)
            print(f"Text extracted from image and saved to: {output_text_file}")
            return f"Text successfully extracted to {output_text_file}"

        return extracted_text

    except Exception as e:
        error_msg = f"Error extracting text from image: {e}"
        print(error_msg)
        return error_msg


def batch_extract_text_from_images(image_folder, output_text_file):
    """
    Extract text from all images in a folder and combine into one text file

    Args:
        image_folder (str): Path to folder containing images
        output_text_file (str): Path to save combined extracted text
    """
    try:
        # Get all image files
        image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif')
        image_files = [
            os.path.join(image_folder, f)
            for f in os.listdir(image_folder)
            if f.lower().endswith(image_extensions)
        ]
        image_files.sort()

        if not image_files:
            print("No image files found in the specified folder.")
            return

        combined_text = ""

        for i, image_path in enumerate(image_files):
            print(f"Processing image {i+1}/{len(image_files)}: {os.path.basename(image_path)}")

            # Extract text from image
            text = extract_text_from_image(image_path)

            if text and text.strip():
                combined_text += f"\n--- {os.path.basename(image_path)} ---\n"
                combined_text += text
                combined_text += "\n" + "="*50 + "\n"

        # Save combined text
        with open(output_text_file, 'w', encoding='utf-8') as f:
            f.write(combined_text)

        print(f"Text extracted from {len(image_files)} images and saved to: {output_text_file}")

    except Exception as e:
        print(f"Error in batch text extraction: {e}")


# Example usage
if __name__ == "__main__":
    # Convert ZIP images to PDF
    # convert_zip_images_to_pdf("Screenshot 2025-07-07 171612.zip", "output.pdf")

    # Extract text from PDF (with images)
    # text = extract_text_from_pdf_images("output.pdf", "extracted_text.txt")

    # Extract text from single image
    # text = extract_text_from_image("image.png", "image_text.txt")

    # Extract text from all images in a folder
    # batch_extract_text_from_images("image_folder", "combined_text.txt")

    pass
