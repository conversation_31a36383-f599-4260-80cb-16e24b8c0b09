import json

with open('Test.json', 'r') as file:
    data = json.load(file)

items = data['result']['items']

max_net_sales = float('-inf')
best_platform = ''
best_store = ''

for item in items:
    sales = item['sales']
    cancel = item['cancel']
    net_sales = sales - cancel

    if net_sales > max_net_sales:
        max_net_sales = net_sales
        best_platform = item['platformName']
        best_store = item['storeName']

print(f"Platform: {best_platform}")
print(f"Store : {best_store}")
print(f"Highest Net Sales: {max_net_sales}")
