#!/usr/bin/env python3
"""
Example usage of PDF and image text extraction functionality
"""

from pdf import (
    convert_zip_images_to_pdf,
    extract_text_from_pdf_images,
    extract_text_from_image,
    batch_extract_text_from_images
)
import os

def main():
    print("PDF and Image Text Extraction Examples")
    print("=" * 50)
    
    # Example 1: Convert ZIP of images to PDF
    print("\n1. Converting ZIP of images to PDF...")
    zip_file = "Screenshot 2025-07-07 171612.zip"
    if os.path.exists(zip_file):
        convert_zip_images_to_pdf(zip_file, "output.pdf")
    else:
        print(f"ZIP file '{zip_file}' not found. Skipping this example.")
    
    # Example 2: Extract text from PDF with images
    print("\n2. Extracting text from PDF...")
    pdf_file = "output.pdf"
    if os.path.exists(pdf_file):
        text = extract_text_from_pdf_images(pdf_file, "extracted_text.txt")
        print("First 200 characters of extracted text:")
        if isinstance(text, str) and len(text) > 200:
            print(text[:200] + "...")
        else:
            print(text)
    else:
        print(f"PDF file '{pdf_file}' not found. Skipping this example.")
    
    # Example 3: Extract text from a single image
    print("\n3. Extracting text from single image...")
    # Look for any image file in current directory
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if image_files:
        image_file = image_files[0]
        print(f"Processing image: {image_file}")
        text = extract_text_from_image(image_file, f"{image_file}_text.txt")
        print("Text extraction completed.")
    else:
        print("No image files found in current directory. Skipping this example.")
    
    # Example 4: Batch extract text from images in a folder
    print("\n4. Batch extracting text from images...")
    if os.path.exists("extracted_images"):
        batch_extract_text_from_images("extracted_images", "batch_extracted_text.txt")
    else:
        print("'extracted_images' folder not found. Skipping this example.")
    
    print("\nAll examples completed!")

if __name__ == "__main__":
    main()
