#!/usr/bin/env python3
"""
Test script to check what functionality is available
"""

from pdf import (
    convert_zip_images_to_pdf,
    extract_text_from_pdf_images,
    extract_text_from_image,
    batch_extract_text_from_images,
    check_tesseract
)
import os

def test_system_requirements():
    """Test what functionality is available on this system"""
    print("=== System Requirements Check ===")
    
    # Check Tesseract OCR
    tesseract_ok, tesseract_msg = check_tesseract()
    print(f"Tesseract OCR: {'✓ Available' if tesseract_ok else '✗ Not Available'}")
    if not tesseract_ok:
        print(f"  Details: {tesseract_msg}")
        print("  Install from: https://github.com/UB-Mannheim/tesseract/wiki")
    else:
        print(f"  Details: {tesseract_msg}")
    
    print()

def test_available_functionality():
    """Test the functionality that's currently available"""
    print("=== Testing Available Functionality ===")
    
    # Test 1: ZIP to PDF conversion (should work)
    print("\n1. Testing ZIP to PDF conversion...")
    zip_file = "Screenshot 2025-07-07 171612.zip"
    if os.path.exists(zip_file):
        try:
            convert_zip_images_to_pdf(zip_file, "test_output.pdf")
            print("✓ ZIP to PDF conversion successful!")
        except Exception as e:
            print(f"✗ ZIP to PDF conversion failed: {e}")
    else:
        print(f"✗ Test ZIP file '{zip_file}' not found")
    
    # Test 2: OCR functionality (may not work without Tesseract)
    print("\n2. Testing OCR functionality...")
    
    # Check if we have any image files to test with
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if image_files:
        test_image = image_files[0]
        print(f"Testing with image: {test_image}")
        
        try:
            result = extract_text_from_image(test_image, "test_ocr_output.txt")
            if "OCR not available" in result:
                print("✗ OCR functionality not available (Tesseract not installed)")
            else:
                print("✓ OCR functionality working!")
                print(f"Text extracted to: test_ocr_output.txt")
        except Exception as e:
            print(f"✗ OCR test failed: {e}")
    else:
        print("✗ No image files found for OCR testing")
    
    # Test 3: PDF text extraction (may not work without Tesseract)
    print("\n3. Testing PDF text extraction...")
    if os.path.exists("test_output.pdf"):
        try:
            result = extract_text_from_pdf_images("test_output.pdf", "test_pdf_text.txt")
            if "OCR not available" in result:
                print("✗ PDF text extraction not available (Tesseract not installed)")
            else:
                print("✓ PDF text extraction working!")
                print(f"Text extracted to: test_pdf_text.txt")
        except Exception as e:
            print(f"✗ PDF text extraction failed: {e}")
    else:
        print("✗ No PDF file available for testing")

def main():
    print("PDF and Image Processing Tool - Functionality Test")
    print("=" * 60)
    
    test_system_requirements()
    test_available_functionality()
    
    print("\n=== Summary ===")
    tesseract_ok, _ = check_tesseract()
    
    if tesseract_ok:
        print("✓ All functionality is available!")
        print("You can use all features including OCR text extraction.")
    else:
        print("⚠ Limited functionality available")
        print("✓ ZIP to PDF conversion works")
        print("✗ OCR text extraction requires Tesseract installation")
        print("\nTo enable full functionality:")
        print("1. Download Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install it and add to your system PATH")
        print("3. Run this test again to verify")

if __name__ == "__main__":
    main()
