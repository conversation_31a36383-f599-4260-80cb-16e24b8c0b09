# Installing Tesseract OCR on Windows

To enable the OCR (Optical Character Recognition) functionality for extracting text from PDF images, you need to install Tesseract OCR.

## Quick Installation Steps

### Method 1: Download Installer (Recommended)

1. **Download Tesseract**
   - Go to: https://github.com/UB-<PERSON>heim/tesseract/wiki
   - Download the latest Windows installer (e.g., `tesseract-ocr-w64-setup-5.3.3.20231005.exe`)

2. **Install Tesseract**
   - Run the downloaded installer
   - **Important**: During installation, note the installation path (usually `C:\Program Files\Tesseract-OCR`)
   - Complete the installation

3. **Add to System PATH**
   - Open Windows Settings → System → About → Advanced system settings
   - Click "Environment Variables"
   - Under "System variables", find and select "Path", then click "Edit"
   - Click "New" and add: `C:\Program Files\Tesseract-OCR`
   - Click "OK" to save all changes

4. **Verify Installation**
   - Open a new Command Prompt or PowerShell
   - Run: `tesseract --version`
   - You should see version information

### Method 2: Using Package Manager

If you have Chocolatey installed:
```bash
choco install tesseract
```

If you have Scoop installed:
```bash
scoop install tesseract
```

## Alternative: Manual Path Configuration

If you don't want to modify your system PATH, you can set the Tesseract path directly in your Python code:

```python
import pytesseract

# Set the path to tesseract executable
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

Add this line at the beginning of your script before using any OCR functions.

## Testing the Installation

After installing Tesseract, run the test script to verify everything works:

```bash
python test_functionality.py
```

You should see:
- ✓ Tesseract OCR: Available
- ✓ All functionality is available!

## Troubleshooting

### Common Issues:

1. **"tesseract is not recognized"**
   - Tesseract is not in your PATH
   - Restart your command prompt/PowerShell after adding to PATH
   - Verify the installation path is correct

2. **Permission errors**
   - Run the installer as Administrator
   - Ensure you have write permissions to the installation directory

3. **OCR accuracy is poor**
   - Ensure images have good contrast
   - Use higher resolution images
   - Preprocess images (resize, enhance contrast) before OCR

### Verify Installation Path:

Check if Tesseract is installed in the default location:
```bash
dir "C:\Program Files\Tesseract-OCR"
```

## Language Support

Tesseract supports multiple languages. The default installation includes English. To add more languages:

1. During installation, select additional language packs
2. Or download language data files from: https://github.com/tesseract-ocr/tessdata
3. Place them in the `tessdata` folder in your Tesseract installation directory

## Next Steps

Once Tesseract is installed and working:

1. Run `python test_functionality.py` to verify everything works
2. Try the example usage: `python example_usage.py`
3. Use the OCR functions in your own scripts

The OCR functionality will now be available for:
- Extracting text from PDF images
- Converting single images to text
- Batch processing multiple images
