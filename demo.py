#!/usr/bin/env python3
"""
Demo script showing current functionality
"""

from pdf import convert_zip_images_to_pdf, check_tesseract
import os

def main():
    print("PDF and Image Processing Tool - Demo")
    print("=" * 50)
    
    # Check what's available
    tesseract_ok, tesseract_msg = check_tesseract()
    print(f"Tesseract OCR: {'✓ Available' if tesseract_ok else '✗ Not Available'}")
    
    # Demo 1: ZIP to PDF conversion (this works)
    print("\n1. ZIP to PDF Conversion Demo")
    print("-" * 30)
    
    zip_file = "Screenshot 2025-07-07 171612.zip"
    if os.path.exists(zip_file):
        print(f"Converting {zip_file} to PDF...")
        convert_zip_images_to_pdf(zip_file, "demo_output.pdf")
        print("✓ Conversion completed!")
        
        if os.path.exists("demo_output.pdf"):
            file_size = os.path.getsize("demo_output.pdf")
            print(f"✓ Output file created: demo_output.pdf ({file_size} bytes)")
    else:
        print(f"✗ ZIP file '{zip_file}' not found")
    
    # Demo 2: OCR functionality (requires Tesseract)
    print("\n2. OCR Text Extraction Demo")
    print("-" * 30)
    
    if tesseract_ok:
        print("✓ OCR functionality is available!")
        print("You can now use:")
        print("  - extract_text_from_pdf_images()")
        print("  - extract_text_from_image()")
        print("  - batch_extract_text_from_images()")
    else:
        print("✗ OCR functionality requires Tesseract installation")
        print("To enable OCR:")
        print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install and add to PATH")
        print("3. Run this demo again")
    
    print("\n" + "=" * 50)
    print("Demo completed!")

if __name__ == "__main__":
    main()
