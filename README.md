# PDF and Image Text Extraction Tool

This tool provides functionality to convert images to PDF and extract text from PDF images using OCR (Optical Character Recognition).

## Features

1. **Convert ZIP of images to PDF** - Convert a ZIP file containing images into a single PDF
2. **Extract text from PDF images** - Use OCR to extract text from PDF files containing images
3. **Extract text from single image** - Extract text from individual image files
4. **Batch extract text from images** - Process multiple images in a folder and combine extracted text

## Installation

### Prerequisites

1. **Python 3.7+** is required
2. **Tesseract OCR** must be installed on your system:

#### Windows:
- Download and install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
- Add Tesseract to your system PATH, or set the path in your code:
  ```python
  import pytesseract
  pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
  ```

#### macOS:
```bash
brew install tesseract
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt-get install tesseract-ocr
```

### Python Dependencies

Install the required Python packages:

```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install Pillow fpdf2 pytesseract pdf2image PyMuPDF
```

## Usage

### Import the functions

```python
from pdf import (
    convert_zip_images_to_pdf,
    extract_text_from_pdf_images,
    extract_text_from_image,
    batch_extract_text_from_images
)
```

### 1. Convert ZIP of images to PDF

```python
convert_zip_images_to_pdf("images.zip", "output.pdf")
```

### 2. Extract text from PDF with images

```python
# Extract and save to file
extract_text_from_pdf_images("document.pdf", "extracted_text.txt")

# Extract and return as string
text = extract_text_from_pdf_images("document.pdf")
print(text)
```

### 3. Extract text from single image

```python
# Extract and save to file
extract_text_from_image("image.png", "image_text.txt")

# Extract and return as string
text = extract_text_from_image("image.png")
print(text)
```

### 4. Batch extract text from multiple images

```python
batch_extract_text_from_images("image_folder", "combined_text.txt")
```

## Example Usage

Run the example script to see all functions in action:

```bash
python example_usage.py
```

## Supported Image Formats

- PNG
- JPG/JPEG
- BMP
- TIFF
- GIF

## OCR Languages

By default, the tool uses English for OCR. To use other languages, modify the `lang` parameter in the OCR calls:

```python
# For multiple languages
text = pytesseract.image_to_string(img, lang='eng+fra+deu')

# For specific language
text = pytesseract.image_to_string(img, lang='spa')  # Spanish
```

Available language codes can be found in the Tesseract documentation.

## Error Handling

The tool includes robust error handling:
- Falls back to alternative PDF processing methods if one fails
- Provides clear error messages for debugging
- Handles various image formats gracefully

## Performance Notes

- **PyMuPDF** is used as the primary PDF processing library for better performance
- **pdf2image** is used as a fallback method
- For large PDFs or batch processing, consider processing in chunks to manage memory usage

## Troubleshooting

### Common Issues

1. **"tesseract is not recognized"** - Ensure Tesseract is installed and in your PATH
2. **Poor OCR accuracy** - Try preprocessing images (resize, enhance contrast, etc.)
3. **Memory issues with large PDFs** - Process pages individually or in smaller batches

### Tips for Better OCR Results

- Use high-resolution images
- Ensure good contrast between text and background
- Avoid skewed or rotated text when possible
- Clean images (remove noise, artifacts) before OCR
